/* Test Runner Styles - Microsoft Teams UI Compatible */

:root {
    /* Teams color palette */
    --teams-base: #f3f2f1;
    --teams-primary: #6264a7;
    --teams-primary-hover: #7174b4;
    --teams-success: #92c353;
    --teams-danger: #d13438;
    --teams-warning: #ffaa44;
    --teams-info: #2d8cff;
    --teams-dark: #252423;
    --teams-light: #ffffff;
    --teams-border: #e1dfdd;
    --teams-text: #252423;
    --teams-text-light: #f3f2f1;
}

body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
    background-color: var(--teams-base);
    color: var(--teams-text);
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow-x: hidden;
}

/* Header */
.ms-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--teams-primary);
    color: var(--teams-light);
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.ms-header-brand {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--teams-light);
    text-decoration: none;
}

.ms-header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ms-environment-display {
    font-size: 0.875rem;
    color: var(--teams-light);
}

/* Layout */
.ms-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 56px);
}

.ms-layout {
    display: flex;
    flex: 1;
}

/* Navigation */
.ms-nav {
    width: 240px;
    background-color: var(--teams-light);
    border-right: 1px solid var(--teams-border);
    overflow-y: auto;
}

.ms-nav-content {
    padding: 1rem 0;
}

.ms-nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ms-nav-item {
    margin: 0;
}

.ms-nav-link {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--teams-text);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 400;
    transition: background-color 0.2s;
}

.ms-nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.ms-nav-link-active {
    background-color: rgba(98, 100, 167, 0.1);
    border-left: 3px solid var(--teams-primary);
    font-weight: 600;
    color: var(--teams-primary);
}

/* Content */
.ms-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.ms-content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--teams-border);
}

.ms-section-title {
    margin: 1.5rem 0 1rem;
    font-weight: 600;
}

/* Grid */
.ms-grid {
    display: flex;
    flex-direction: column;
    margin: 0 -0.75rem;
    width: 100%;
}

.ms-grid-row {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 1.5rem;
    align-items: stretch;
    justify-content: space-between;
}

.ms-grid-col {
    padding: 0 0.75rem;
    margin-bottom: 1.5rem;
    box-sizing: border-box;
    display: flex;
}

.ms-sm3 {
    width: 25%;
    flex: 0 0 calc(25% - 1rem);
    max-width: calc(25% - 1rem);
}

.ms-sm4 {
    width: 33.333%;
    flex: 0 0 calc(33.333% - 1rem);
    max-width: calc(33.333% - 1rem);
}

.ms-sm6 {
    width: 50%;
    flex: 0 0 calc(50% - 1rem);
    max-width: calc(50% - 1rem);
}

.ms-sm8 {
    width: 66.666%;
    flex: 0 0 calc(66.666% - 1rem);
    max-width: calc(66.666% - 1rem);
}

/* Cards */
.ms-card {
    background-color: var(--teams-light);
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
    margin-bottom: 1.5rem;
    width: 100%;
    height: 100%;
    justify-content: center;
}

.ms-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ms-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--teams-border);
}

.ms-card-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--teams-text);
    flex: 1;
    padding-right: 0.5rem;
    line-height: 1.3;
}

.ms-card-activity {
    font-size: 0.875rem;
    color: var(--teams-text);
    opacity: 0.7;
}

.ms-card-count {
    font-weight: 600;
    color: var(--teams-primary);
}

.ms-card-body {
    margin-bottom: 0.75rem;
}

/* Forms */
.ms-form-group {
    margin-bottom: 1rem;
}

.ms-Label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--teams-text);
    margin-bottom: 0.25rem;
}

.ms-TextField-field {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--teams-border);
    border-radius: 2px;
    font-size: 0.875rem;
    font-family: inherit;
    background-color: var(--teams-light);
    color: var(--teams-text);
    transition: border-color 0.2s;
}

.ms-TextField-field:focus {
    outline: none;
    border-color: var(--teams-primary);
    box-shadow: 0 0 0 1px var(--teams-primary);
}

.ms-TextField-description {
    font-size: 0.75rem;
    color: var(--teams-text);
    opacity: 0.7;
    margin-top: 0.25rem;
}

.ms-Dropdown {
    position: relative;
}

.ms-Dropdown-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--teams-border);
    border-radius: 2px;
    font-size: 0.875rem;
    font-family: inherit;
    background-color: var(--teams-light);
    color: var(--teams-text);
    cursor: pointer;
}

.ms-Dropdown-select:focus {
    outline: none;
    border-color: var(--teams-primary);
    box-shadow: 0 0 0 1px var(--teams-primary);
}

/* Buttons */
.ms-Button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-radius: 2px;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    min-height: 32px;
    box-sizing: border-box;
}

.ms-Button--primary {
    background-color: var(--teams-primary);
    color: var(--teams-light);
    border-color: var(--teams-primary);
}

.ms-Button--primary:hover {
    background-color: var(--teams-primary-hover);
    border-color: var(--teams-primary-hover);
}

.ms-Button--default {
    background-color: var(--teams-light);
    color: var(--teams-text);
    border-color: var(--teams-border);
}

.ms-Button--default:hover {
    background-color: var(--teams-base);
    border-color: var(--teams-primary);
}

.ms-Button-label {
    font-weight: inherit;
}

/* Tables */
.ms-Table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.ms-Table th,
.ms-Table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid var(--teams-border);
}

.ms-Table th {
    font-weight: 600;
    background-color: var(--teams-base);
    color: var(--teams-text);
}

/* Test Statistics */
.test-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.test-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--teams-border);
}

.test-stat:last-child {
    border-bottom: none;
}

.test-stat-label {
    font-size: 0.875rem;
    color: var(--teams-text);
}

.test-stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--teams-text);
}

.test-passed {
    color: var(--teams-success);
}

.test-failed {
    color: var(--teams-danger);
}

/* Modal */
.ms-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.ms-modal.active {
    display: flex;
}

.ms-modal-content {
    background-color: var(--teams-light);
    border-radius: 4px;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.ms-modal-content h2 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: var(--teams-text);
}

.ms-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

/* Empty State */
.ms-empty-message {
    text-align: center;
    color: var(--teams-text);
    opacity: 0.7;
    padding: 2rem;
    font-style: italic;
}

/* Panel Content */
.ms-panel-content {
    padding: 1rem;
}

/* Search Results */
.search-results {
    max-height: 400px;
    overflow-y: auto;
}

/* Active Tests */
.active-tests {
    max-height: 300px;
    overflow-y: auto;
}

/* Test Status Bar */
.test-status-bar {
    position: sticky;
    top: 0;
    background-color: var(--teams-light);
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 50;
}

.test-status-bar.hidden {
    display: none;
}

.status-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
}

.status-left,
.status-center,
.status-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-center {
    flex: 1;
    justify-content: center;
}

.status-timer {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--teams-text);
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-bar {
    width: 200px;
    height: 8px;
    background-color: var(--teams-border);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--teams-primary);
    transition: width 0.3s ease;
    width: 0%;
}

.status-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-height: auto;
}

/* Floating Action Button */
.floating-test-runner {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.fab-main {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--teams-primary);
    color: var(--teams-light);
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.fab-main:hover {
    background-color: var(--teams-primary-hover);
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.fab-icon {
    font-size: 1.25rem;
}

.quick-test-panel {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 320px;
    background-color: var(--teams-light);
    border: 1px solid var(--teams-border);
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: translateY(10px);
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.quick-test-panel:not(.hidden) {
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.quick-test-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--teams-border);
}

.quick-test-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--teams-text);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--teams-text);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background-color: var(--teams-base);
    border-radius: 2px;
}

.quick-test-body {
    padding: 1rem;
}

.quick-input-group {
    margin-bottom: 1rem;
}

.quick-input-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--teams-text);
    margin-bottom: 0.25rem;
}

.quick-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--teams-border);
    border-radius: 2px;
    font-size: 0.875rem;
    font-family: inherit;
    background-color: var(--teams-light);
    color: var(--teams-text);
    box-sizing: border-box;
}

.quick-input:focus {
    outline: none;
    border-color: var(--teams-primary);
    box-shadow: 0 0 0 1px var(--teams-primary);
}

.quick-settings-info {
    background-color: var(--teams-base);
    border-radius: 4px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.settings-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.settings-label {
    font-weight: 600;
    color: var(--teams-text);
}

.settings-note {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--teams-text);
    opacity: 0.7;
}

.quick-actions {
    display: flex;
    justify-content: flex-end;
}

.quick-run-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--teams-primary);
    color: var(--teams-light);
    border: none;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.quick-run-btn:hover {
    background-color: var(--teams-primary-hover);
}

.btn-icon {
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ms-nav {
        width: 200px;
    }

    .ms-content {
        padding: 1rem;
    }

    .ms-grid-row {
        flex-direction: column;
    }

    .ms-sm3,
    .ms-sm4,
    .ms-sm6,
    .ms-sm8 {
        width: 100%;
        flex: none;
        max-width: none;
    }

    .floating-test-runner {
        bottom: 1rem;
        right: 1rem;
    }

    .quick-test-panel {
        width: 280px;
    }
}
