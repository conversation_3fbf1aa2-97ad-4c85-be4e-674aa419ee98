/**
 * Sticky Status Bar for Test Execution
 * Provides real-time updates and navigation without scrolling
 */

class TestStatusBar {
    constructor() {
        this.statusBar = document.getElementById('test-status-bar');
        this.currentTestIcon = document.getElementById('current-test-icon');
        this.currentTestInfo = document.getElementById('current-test-info');
        this.testTimer = document.getElementById('test-timer');
        this.progressContainer = document.getElementById('test-progress-container');
        this.progressText = document.getElementById('test-progress-text');
        this.progressFill = document.getElementById('test-progress-fill');
        this.viewActiveTestsBtn = document.getElementById('view-active-tests-btn');
        this.viewResultsBtn = document.getElementById('view-results-btn');
        this.dismissBtn = document.getElementById('dismiss-status-btn');
        this.demoBtn = document.getElementById('test-status-demo-btn');
        
        this.currentTest = null;
        this.startTime = null;
        this.timerInterval = null;
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Navigation buttons
        this.viewActiveTestsBtn?.addEventListener('click', () => this.scrollToActiveTests());
        this.viewResultsBtn?.addEventListener('click', () => this.scrollToResults());
        this.dismissBtn?.addEventListener('click', () => this.hide());
        this.demoBtn?.addEventListener('click', () => this.runDemo());
        
        // Listen for test events
        document.addEventListener('test-started', (event) => this.onTestStarted(event.detail));
        document.addEventListener('test-progress', (event) => this.onTestProgress(event.detail));
        document.addEventListener('test-completed', (event) => this.onTestCompleted(event.detail));
        document.addEventListener('test-failed', (event) => this.onTestFailed(event.detail));
    }

    show() {
        this.statusBar?.classList.remove('hidden');
    }

    hide() {
        this.statusBar?.classList.add('hidden');
        this.clearTimer();
        this.currentTest = null;
    }

    onTestStarted(testData) {
        console.log('Status bar: Test started', testData);
        
        this.currentTest = testData;
        this.startTime = Date.now();
        
        // Update UI with better icons
        this.currentTestIcon.textContent = '⚡';
        this.currentTestInfo.textContent = `Test ${testData.testId || testData.id} Running...`;
        
        // Show timer and progress
        this.testTimer.classList.remove('hidden');
        this.progressContainer.classList.remove('hidden');
        this.viewActiveTestsBtn.classList.remove('hidden');
        
        // Set status bar state
        this.statusBar.className = 'test-status-bar status-running';
        
        // Start timer
        this.startTimer();
        
        // Show status bar
        this.show();
        
        // Auto-scroll to active tests section
        setTimeout(() => this.scrollToActiveTests(), 500);
    }

    onTestProgress(progressData) {
        if (!this.currentTest) return;
        
        console.log('Status bar: Test progress', progressData);
        
        const progress = progressData.progress || 0;
        this.progressText.textContent = `${progress}%`;
        this.progressFill.style.width = `${progress}%`;
    }

    onTestCompleted(testData) {
        console.log('Status bar: Test completed', testData);
        
        this.clearTimer();
        
        // Update UI with success styling
        this.currentTestIcon.textContent = '🎉';
        this.currentTestInfo.textContent = `Test ${testData.testId || testData.id} Completed Successfully`;
        
        // Hide progress, show results button
        this.progressContainer.classList.add('hidden');
        this.viewActiveTestsBtn.classList.add('hidden');
        this.viewResultsBtn.classList.remove('hidden');
        
        // Set status bar state
        this.statusBar.className = 'test-status-bar status-passed';
        
        // Auto-scroll to results after a delay
        setTimeout(() => this.scrollToResults(), 1000);
        
        // Auto-hide after 10 seconds
        setTimeout(() => this.hide(), 10000);
    }

    onTestFailed(testData) {
        console.log('Status bar: Test failed', testData);
        
        this.clearTimer();
        
        // Update UI with failure styling
        this.currentTestIcon.textContent = '💥';
        this.currentTestInfo.textContent = `Test ${testData.testId || testData.id} Failed`;
        
        // Hide progress, show results button
        this.progressContainer.classList.add('hidden');
        this.viewActiveTestsBtn.classList.add('hidden');
        this.viewResultsBtn.classList.remove('hidden');
        
        // Set status bar state
        this.statusBar.className = 'test-status-bar status-failed';
        
        // Auto-scroll to results after a delay
        setTimeout(() => this.scrollToResults(), 1000);
        
        // Auto-hide after 15 seconds (longer for failures)
        setTimeout(() => this.hide(), 15000);
    }

    startTimer() {
        this.clearTimer();
        
        this.timerInterval = setInterval(() => {
            if (!this.startTime) return;
            
            const elapsed = Date.now() - this.startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            this.testTimer.textContent = `⏱️ ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    clearTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }

    scrollToActiveTests() {
        const activeTestsSection = document.querySelector('#active-tests-panel');
        if (activeTestsSection) {
            // Scroll to the parent card for better visibility
            const parentCard = activeTestsSection.closest('.ms-DocumentCard');
            const targetElement = parentCard || activeTestsSection;

            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
            this.highlightSection(targetElement);
        } else {
            console.warn('Active tests section not found');
        }
    }

    scrollToResults() {
        const resultsSection = document.querySelector('#recent-runs-table');
        if (resultsSection) {
            // Scroll to the parent card for better visibility
            const parentCard = resultsSection.closest('.ms-DocumentCard');
            const targetElement = parentCard || resultsSection;

            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
            this.highlightSection(targetElement);
        } else {
            console.warn('Results section not found');
        }
    }

    highlightSection(element) {
        // Add temporary highlight
        element.style.transition = 'background-color 0.3s ease';
        element.style.backgroundColor = '#fff4ce';
        
        setTimeout(() => {
            element.style.backgroundColor = '';
            setTimeout(() => {
                element.style.transition = '';
            }, 300);
        }, 2000);
    }

    // Public methods for external integration
    updateTestStatus(testId, status, progress = null) {
        if (status === 'running') {
            this.onTestStarted({ testId, id: testId });
        } else if (status === 'completed' || status === 'passed') {
            this.onTestCompleted({ testId, id: testId });
        } else if (status === 'failed') {
            this.onTestFailed({ testId, id: testId });
        }
        
        if (progress !== null) {
            this.onTestProgress({ progress });
        }
    }

    showMessage(message, type = 'info') {
        this.currentTestInfo.textContent = message;
        
        const iconMap = {
            'info': 'ℹ️',
            'warning': '⚠️',
            'error': '❌',
            'success': '✅'
        };
        
        this.currentTestIcon.textContent = iconMap[type] || 'ℹ️';
        this.statusBar.className = `test-status-bar status-${type}`;
        
        this.show();
        
        // Auto-hide after 5 seconds for messages
        setTimeout(() => this.hide(), 5000);
    }

    // Demo function to test the status bar
    runDemo() {
        console.log('Running status bar demo...');

        // Start demo test
        this.onTestStarted({ testId: 'DEMO-123', id: 'DEMO-123' });

        // Simulate progress updates
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += 10;
            this.onTestProgress({ progress });

            if (progress >= 100) {
                clearInterval(progressInterval);

                // Randomly complete or fail
                const success = Math.random() > 0.3; // 70% success rate
                if (success) {
                    setTimeout(() => this.onTestCompleted({ testId: 'DEMO-123', id: 'DEMO-123' }), 500);
                } else {
                    setTimeout(() => this.onTestFailed({ testId: 'DEMO-123', id: 'DEMO-123' }), 500);
                }
            }
        }, 500);
    }
}

// Initialize status bar when DOM is ready
let statusBar;

document.addEventListener('DOMContentLoaded', () => {
    statusBar = new TestStatusBar();
    console.log('Test Status Bar initialized');
    
    // Make it globally available
    window.testStatusBar = statusBar;
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TestStatusBar;
}
