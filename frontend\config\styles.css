/* Teams UI Styling for Configuration Page */

:root {
    /* Teams primary colors */
    --teams-base: #f3f2f1;
    --teams-primary: #6264a7;
    --teams-secondary: #11100f;
    --teams-accent: #5b5fc7;
    
    /* Teams neutral colors */
    --teams-neutral-lighter: #f3f2f1;
    --teams-neutral-light: #edebe9;
    --teams-neutral: #a19f9d;
    --teams-neutral-dark: #605e5c;
    --teams-neutral-darker: #323130;

    /* Teams semantic colors */
    --teams-success: #107c10;
    --teams-warning: #ffaa44;
    --teams-error: #a4262c;
    --teams-info: #0078d4;
}

body {
    font-family: 'Segoe UI', sans-serif;
    background-color: var(--teams-base);
    color: var(--teams-secondary);
    margin: 0;
    padding: 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Teams Header */
.teams-header {
    background-color: var(--teams-primary);
    color: white;
    height: 48px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.teams-app-title {
    font-size: 16px;
    font-weight: 600;
    padding: 0 16px;
}

.teams-environment-display {
    font-size: 14px;
    padding: 0 16px;
}

/* Teams Layout */
.teams-container {
    width: 100%;
    height: calc(100vh - 48px);
    overflow: hidden;
}

.teams-layout {
    display: flex;
    height: 100%;
}

/* Teams Navigation */
.teams-nav {
    width: 220px;
    background-color: var(--teams-neutral-lighter);
    border-right: 1px solid var(--teams-neutral-light);
    height: 100%;
    overflow-y: auto;
    padding-top: 16px;
}

.ms-Nav-group {
    margin-bottom: 16px;
}

.ms-Nav-groupContent {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ms-Nav-item {
    margin: 4px 0;
}

.ms-Nav-link {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    color: var(--teams-neutral-darker);
    text-decoration: none;
    font-weight: 400;
    border-left: 3px solid transparent;
}

.ms-Nav-link:hover {
    background-color: var(--teams-neutral-light);
    color: var(--teams-primary);
    text-decoration: none;
}

.ms-Nav-link.is-selected {
    background-color: #e1dfdd;
    border-left: 3px solid var(--teams-primary);
    color: var(--teams-primary);
    font-weight: 600;
}

.ms-Icon {
    margin-right: 8px;
    font-size: 16px;
}

/* Teams Content Area */
.teams-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.teams-content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--teams-neutral-light);
}

.teams-content-header h1 {
    margin: 0;
    font-weight: 600;
    color: var(--teams-neutral-darker);
}

.teams-action-bar {
    display: flex;
    gap: 8px;
}

/* Teams Grid System */
.teams-grid {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
}

.teams-grid-col-8 {
    width: calc(66.666% - 16px);
    margin: 0 8px;
}

.teams-grid-col-4 {
    width: calc(33.333% - 16px);
    margin: 0 8px;
}

/* Teams Card */
.teams-card {
    background-color: white;
    border-radius: 2px;
    box-shadow: 0 1.6px 3.6px 0 rgba(0, 0, 0, 0.132), 0 0.3px 0.9px 0 rgba(0, 0, 0, 0.108);
    margin-bottom: 16px;
}

.ms-DocumentCard-title {
    padding: 16px;
    border-bottom: 1px solid var(--teams-neutral-light);
}

.ms-DocumentCard-title h4 {
    margin: 0;
    font-weight: 600;
    color: var(--teams-neutral-darker);
}

.ms-DocumentCard-details {
    padding: 16px;
}

/* Teams Form Elements */
.teams-form-group {
    margin-bottom: 16px;
}

.ms-Label {
    display: block;
    margin-bottom: 4px;
    font-weight: 600;
    color: var(--teams-neutral-darker);
}

.ms-TextField-field {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid var(--teams-neutral);
    border-radius: 2px;
    font-family: 'Segoe UI', sans-serif;
    font-size: 14px;
    color: var(--teams-neutral-darker);
}

.ms-TextField-field:focus {
    border-color: var(--teams-primary);
    outline: none;
    box-shadow: 0 0 0 1px var(--teams-primary);
}

.ms-TextField-description {
    font-size: 12px;
    color: var(--teams-neutral-dark);
    margin-top: 4px;
}

.ms-Dropdown {
    position: relative;
    width: 100%;
}

.ms-Dropdown-select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid var(--teams-neutral);
    border-radius: 2px;
    background-color: white;
    font-family: 'Segoe UI', sans-serif;
    font-size: 14px;
    color: var(--teams-neutral-darker);
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23323130' d='M6 8.5l-4-4h8z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
}

.ms-Dropdown-select:focus {
    border-color: var(--teams-primary);
    outline: none;
    box-shadow: 0 0 0 1px var(--teams-primary);
}

/* Teams Button */
.ms-Button {
    min-width: 80px;
    height: 32px;
    padding: 0 16px;
    border: none;
    border-radius: 2px;
    font-family: 'Segoe UI', sans-serif;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.1s ease;
}

.ms-Button--primary {
    background-color: var(--teams-primary);
    color: white;
}

.ms-Button--primary:hover {
    background-color: var(--teams-accent);
}

.ms-Button--primary:active {
    background-color: #4b4d9b;
}

.ms-Button-label {
    font-weight: 600;
}

/* Teams Checkbox */
.ms-CheckBox {
    display: flex;
    align-items: center;
    position: relative;
}

.ms-CheckBox-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.ms-CheckBox-field {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.ms-CheckBox-field::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 1px solid var(--teams-neutral);
    border-radius: 2px;
    margin-right: 8px;
    background-color: white;
}

.ms-CheckBox-input:checked + .ms-CheckBox-field::before {
    background-color: var(--teams-primary);
    border-color: var(--teams-primary);
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='white' d='M4.5 8.5L2 6l-1 1 3.5 3.5L11 4l-1-1z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
}

.ms-CheckBox-input:focus + .ms-CheckBox-field::before {
    box-shadow: 0 0 0 1px var(--teams-primary);
}

/* Teams Section Title */
.teams-section-title {
    margin: 16px 0 8px;
    font-weight: 600;
    color: var(--teams-neutral-darker);
}

/* Teams Utilities */
.teams-hidden {
    display: none;
}

/* Toast Notifications - Teams Style */
.teams-toast {
    position: fixed;
    top: 64px;
    right: 16px;
    padding: 12px 16px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    min-width: 250px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1000;
}

.teams-toast.show {
    transform: translateY(0);
    opacity: 1;
}

.teams-toast-success {
    background-color: #e6f4ea;
    color: #0d652d;
    border-left: 4px solid var(--teams-success);
}

.teams-toast-error {
    background-color: #fdecea;
    color: #8e2924;
    border-left: 4px solid var(--teams-error);
}

.teams-toast-warning {
    background-color: #fff8e6;
    color: #8a5700;
    border-left: 4px solid var(--teams-warning);
}

.teams-toast-info {
    background-color: #e8f1fa;
    color: #0c559d;
    border-left: 4px solid var(--teams-info);
}

.teams-toast-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    margin-right: 12px;
    font-weight: bold;
}

.teams-toast-message {
    flex: 1;
}

/* Responsive Design for Teams UI */
@media (max-width: 768px) {
    .teams-layout {
        flex-direction: column;
    }
    
    .teams-nav {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--teams-neutral-light);
    }
    
    .teams-grid-col-8,
    .teams-grid-col-4 {
        width: 100%;
        margin: 0;
    }
    
    .teams-content-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .teams-action-bar {
        margin-top: 8px;
    }
}

/* Sticky Test Status Bar - Modern Design */
.test-status-bar {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    color: white;
    padding: 16px 24px;
    box-shadow: 0 4px 20px rgba(0, 120, 212, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 0 0 16px 16px;
    margin-bottom: 20px;
    animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.test-status-bar.hidden {
    display: none;
}

.status-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    gap: 16px;
}

.status-left {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.status-center {
    display: flex;
    align-items: center;
    flex: 2;
    justify-content: center;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: flex-end;
}

#current-test-icon {
    font-size: 18px;
    margin-right: 8px;
    display: inline-block;
    animation: statusPulse 2s infinite;
}

#current-test-info {
    font-weight: 600;
    font-size: 15px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@keyframes statusPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

.status-timer {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    max-width: 300px;
}

#test-progress-text {
    font-size: 12px;
    font-weight: 600;
    min-width: 35px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ff88 0%, #00cc6a 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 0%;
}

.status-btn {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.25) !important;
    color: white !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    padding: 8px 16px !important;
    height: 36px !important;
    min-height: 36px !important;
    border-radius: 18px !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.status-btn:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.status-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
}

.status-btn .ms-Button-label {
    color: white !important;
    font-size: 13px !important;
    font-weight: 500 !important;
}

/* Animation for status bar appearance */
@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Status bar states */
.test-status-bar.status-running {
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
}

.test-status-bar.status-passed {
    background: linear-gradient(135deg, #107c10 0%, #0e6e0e 100%);
}

.test-status-bar.status-failed {
    background: linear-gradient(135deg, #d13438 0%, #b52e31 100%);
}

.test-status-bar.status-warning {
    background: linear-gradient(135deg, #ff8c00 0%, #e67700 100%);
}

/* Responsive design for status bar */
@media (max-width: 768px) {
    .status-content {
        flex-direction: column;
        gap: 8px;
    }

    .status-left,
    .status-center,
    .status-right {
        flex: none;
        width: 100%;
        justify-content: center;
    }

    .status-right {
        justify-content: center;
    }
}

/* Floating Action Button (FAB) for Quick Test Run */
.floating-test-runner {
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 1001;
}

.fab-main {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    border: none;
    box-shadow: 0 8px 24px rgba(0, 120, 212, 0.4), 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: white;
    font-size: 24px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.fab-main:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 120, 212, 0.5), 0 6px 16px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #106ebe 0%, #005a9e 100%);
}

.fab-main:active {
    transform: scale(1.05) translateY(0);
    box-shadow: 0 6px 20px rgba(0, 120, 212, 0.4), 0 3px 10px rgba(0, 0, 0, 0.15);
}

.fab-icon {
    font-size: 28px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Quick Test Panel */
.quick-test-panel {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 320px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2), 0 8px 24px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transform: translateY(20px) scale(0.95);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
}

.quick-test-panel:not(.hidden) {
    transform: translateY(0) scale(1);
    opacity: 1;
}

.quick-test-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quick-test-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #495057;
}

.quick-test-body {
    padding: 20px;
}

.quick-input-group {
    margin-bottom: 16px;
}

.quick-input-group label {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
}

.quick-input, .quick-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 14px;
    color: #495057;
    background: white;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.quick-input:focus, .quick-select:focus {
    outline: none;
    border-color: #0078d4;
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
}

.quick-actions {
    margin-top: 20px;
}

.quick-run-btn {
    width: 100%;
    padding: 14px 20px;
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 120, 212, 0.3);
}

.quick-run-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 120, 212, 0.4);
    background: linear-gradient(135deg, #106ebe 0%, #005a9e 100%);
}

.quick-run-btn:active {
    transform: translateY(0);
    box-shadow: 0 3px 8px rgba(0, 120, 212, 0.3);
}

.quick-run-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-icon {
    font-size: 16px;
}

.btn-text {
    font-size: 15px;
}

/* Mobile responsiveness for FAB */
@media (max-width: 768px) {
    .floating-test-runner {
        bottom: 16px;
        right: 16px;
    }

    .fab-main {
        width: 56px;
        height: 56px;
        font-size: 20px;
    }

    .quick-test-panel {
        width: calc(100vw - 32px);
        right: -8px;
        bottom: 72px;
    }
}