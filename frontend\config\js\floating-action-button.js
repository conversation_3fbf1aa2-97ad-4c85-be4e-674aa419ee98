/**
 * Floating Action Button (FAB) for Quick Test Execution
 * Provides easy access to test running functionality from anywhere on the page
 */

class FloatingActionButton {
    constructor() {
        this.fabMain = document.getElementById('fab-main');
        this.quickTestPanel = document.getElementById('quick-test-panel');
        this.closeQuickTest = document.getElementById('close-quick-test');
        this.quickTestId = document.getElementById('quick-test-id');
        this.quickProject = document.getElementById('quick-project');
        this.quickRunBtn = document.getElementById('quick-run-btn');
        
        this.isOpen = false;
        
        this.initializeEventListeners();
        this.setupKeyboardShortcuts();
    }

    initializeEventListeners() {
        // FAB main button click
        this.fabMain?.addEventListener('click', () => this.toggleQuickPanel());
        
        // Close button
        this.closeQuickTest?.addEventListener('click', () => this.closeQuickPanel());
        
        // Quick run button
        this.quickRunBtn?.addEventListener('click', () => this.runQuickTest());
        
        // Enter key in test ID input
        this.quickTestId?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.runQuickTest();
            }
        });
        
        // Auto-focus test ID input when panel opens
        this.quickTestPanel?.addEventListener('transitionend', () => {
            if (!this.quickTestPanel.classList.contains('hidden')) {
                this.quickTestId?.focus();
            }
        });
        
        // Click outside to close
        document.addEventListener('click', (e) => {
            if (this.isOpen && !this.quickTestPanel?.contains(e.target) && !this.fabMain?.contains(e.target)) {
                this.closeQuickPanel();
            }
        });
        
        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeQuickPanel();
            }
        });
    }

    setupKeyboardShortcuts() {
        // Ctrl+Shift+R to open quick test panel
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                e.preventDefault();
                this.toggleQuickPanel();
            }
        });
    }

    toggleQuickPanel() {
        if (this.isOpen) {
            this.closeQuickPanel();
        } else {
            this.openQuickPanel();
        }
    }

    openQuickPanel() {
        this.quickTestPanel?.classList.remove('hidden');
        this.fabMain?.style.setProperty('transform', 'rotate(45deg)');
        this.isOpen = true;
        
        // Auto-populate with last selected test if available
        const lastTestId = localStorage.getItem('lastQuickTestId');
        if (lastTestId && this.quickTestId) {
            this.quickTestId.value = lastTestId;
        }
        
        console.log('Quick test panel opened');
    }

    closeQuickPanel() {
        this.quickTestPanel?.classList.add('hidden');
        this.fabMain?.style.setProperty('transform', 'rotate(0deg)');
        this.isOpen = false;
        
        console.log('Quick test panel closed');
    }

    async runQuickTest() {
        const testId = this.quickTestId?.value?.trim();
        const environment = this.quickProject?.value || 'qa02';
        
        if (!testId) {
            this.showError('Please enter a test case ID');
            this.quickTestId?.focus();
            return;
        }
        
        // Validate test ID format (should be numeric)
        if (!/^\d+$/.test(testId)) {
            this.showError('Test ID should be numeric (e.g., 3386)');
            this.quickTestId?.focus();
            return;
        }
        
        try {
            // Save last used test ID
            localStorage.setItem('lastQuickTestId', testId);
            
            // Disable button during execution
            this.setRunButtonState(true, 'Running...');
            
            // Close the panel
            this.closeQuickPanel();
            
            // Show status bar with running state
            if (window.testStatusBar) {
                window.testStatusBar.updateTestStatus(testId, 'running');
            }
            
            // Call the existing test execution function
            await this.executeTest(testId, environment);
            
        } catch (error) {
            console.error('Error running quick test:', error);
            this.showError(`Error: ${error.message || 'Unknown error'}`);
            
            // Re-open panel on error
            this.openQuickPanel();
        } finally {
            // Reset button state
            this.setRunButtonState(false, 'Run Test');
        }
    }

    async executeTest(testId, environment) {
        // Use the existing test execution logic
        if (window.runTestCase && typeof window.runTestCase === 'function') {
            // Use existing function
            const result = await window.runTestCase(testId, environment, 'jps-qa10-app01');
            
            if (result && result.tsn_id) {
                this.showSuccess(`Test ${testId} started successfully! TSN ID: ${result.tsn_id}`);
                
                // Trigger the existing addActiveTest function
                if (window.addActiveTest && typeof window.addActiveTest === 'function') {
                    window.addActiveTest(result);
                }
            } else {
                throw new Error(`Failed to start test ${testId}`);
            }
        } else {
            // Fallback: use API service directly
            if (window.apiService) {
                const result = await window.apiService.runTestCase(testId, {
                    envir: environment,
                    shell_host: 'jps-qa10-app01'
                });
                
                if (result && result.tsn_id) {
                    this.showSuccess(`Test ${testId} started successfully! TSN ID: ${result.tsn_id}`);
                } else {
                    throw new Error(`Failed to start test ${testId}`);
                }
            } else {
                throw new Error('API service not available');
            }
        }
    }

    setRunButtonState(disabled, text) {
        if (this.quickRunBtn) {
            this.quickRunBtn.disabled = disabled;
            const btnText = this.quickRunBtn.querySelector('.btn-text');
            const btnIcon = this.quickRunBtn.querySelector('.btn-icon');
            
            if (btnText) btnText.textContent = text;
            if (btnIcon) btnIcon.textContent = disabled ? '⏳' : '🚀';
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        // Use existing notification system if available
        if (window.showNotification && typeof window.showNotification === 'function') {
            window.showNotification('Quick Test', message, type);
        } else if (window.testStatusBar) {
            window.testStatusBar.showMessage(message, type);
        } else {
            // Fallback to console and alert
            console.log(`${type.toUpperCase()}: ${message}`);
            if (type === 'error') {
                alert(`Error: ${message}`);
            }
        }
    }

    // Public methods for external control
    hide() {
        if (this.fabMain) {
            this.fabMain.style.display = 'none';
        }
    }

    show() {
        if (this.fabMain) {
            this.fabMain.style.display = 'flex';
        }
    }

    setTestId(testId) {
        if (this.quickTestId) {
            this.quickTestId.value = testId;
        }
    }
}

// Initialize FAB when DOM is ready
let floatingActionButton;

document.addEventListener('DOMContentLoaded', () => {
    floatingActionButton = new FloatingActionButton();
    console.log('Floating Action Button initialized');
    
    // Make it globally available
    window.floatingActionButton = floatingActionButton;
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FloatingActionButton;
}
