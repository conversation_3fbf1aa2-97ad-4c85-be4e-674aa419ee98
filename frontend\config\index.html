<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Test Runner</title>
    <!-- Replace Bootstrap with Fluent UI -->
    <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css">
    <link rel="stylesheet" href="styles.css">
    <!-- Teams CSS Framework -->
    <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-js/1.4.0/css/fabric.min.css" />
    <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-js/1.4.0/css/fabric.components.min.css" />
    <!-- Custom Test Search Styles -->
    <link rel="stylesheet" href="css/test-search.css" />
    <!-- API Service as standard script -->
</head>
<body class="ms-Fabric">
    <header class="ms-CommandBar teams-header">
        <div class="ms-CommandBar-mainArea">
            <div class="ms-CommandBar-primaryCommand">
                <div class="teams-app-title">Custom Test Runner</div>
            </div>
            <div class="ms-CommandBar-sideCommands">
                <div class="teams-environment-display" id="environment-display">Current Environment: Development</div>
            </div>
        </div>
    </header>

    <div class="teams-container">
        <div class="teams-layout">
            <nav class="ms-Nav teams-nav">
                <div class="ms-Nav-group">
                    <ul class="ms-Nav-groupContent">
                        <li class="ms-Nav-item">
                            <a class="ms-Nav-link" href="../dashboard/index.html">
                                <i class="ms-Icon ms-Icon--ViewDashboard" aria-hidden="true"></i> Dashboard
                            </a>
                        </li>
                        <li class="ms-Nav-item">
                            <a class="ms-Nav-link is-selected" href="#">
                                <i class="ms-Icon ms-Icon--TestBeaker" aria-hidden="true"></i> Custom Test Runner
                            </a>
                        </li>
                        <li class="ms-Nav-item">
                            <a class="ms-Nav-link" href="../reports/index.html">
                                <i class="ms-Icon ms-Icon--ReportDocument" aria-hidden="true"></i> Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="teams-content">
                <div class="teams-content-header">
                    <h1 class="ms-font-xxl">Custom Test Runner</h1>
                    <div class="teams-action-bar">
                        <button type="button" class="ms-Button ms-Button--primary" id="run-test-btn">
                            <span class="ms-Button-label">Run Test</span>
                        </button>
                    </div>
                </div>

                <div class="teams-content-body">
                    <div class="teams-grid">
                        <div class="teams-grid-col-8">
                            <!-- Test Case Search Card -->
                            <div class="ms-DocumentCard teams-card">
                                <div class="ms-DocumentCard-title">
                                    <h4 class="ms-font-l">Search Test Cases</h4>
                                </div>
                                <div class="ms-DocumentCard-details">
                                    <form id="test-search-form">
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Test Case Name</label>
                                            <input type="text" class="ms-TextField-field" id="test-case-name-search" placeholder="Search by name">
                                        </div>

                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Test Case Comment</label>
                                            <input type="text" class="ms-TextField-field" id="test-case-comment-search" placeholder="Search in comments (e.g., 'payout')">
                                            <div class="ms-TextField-description">Search for keywords in test case comments</div>
                                        </div>

                                        <div class="teams-form-group">
                                            <label class="ms-Label">Status</label>
                                            <div class="ms-Dropdown">
                                                <select class="ms-Dropdown-select" id="test-case-status-filter">
                                                    <option value="">All</option>
                                                    <option value="Active">Active</option>
                                                    <option value="Inactive">Inactive</option>
                                                    <option value="Maintenance">Maintenance</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="teams-form-group">
                                            <button type="submit" class="ms-Button ms-Button--primary" id="search-test-cases-btn">
                                                <span class="ms-Button-label">Search</span>
                                            </button>
                                            <button type="reset" class="ms-Button ms-Button--default">
                                                <span class="ms-Button-label">Clear</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Search Results Card -->
                            <div class="ms-DocumentCard teams-card" id="search-results-card" style="display: none;">
                                <div class="ms-DocumentCard-title">
                                    <h4 class="ms-font-l">Search Results</h4>
                                    <div class="ms-DocumentCard-activity">
                                        <span class="ms-DocumentCard-activityCount" id="search-results-count">0</span> test cases found
                                    </div>
                                </div>
                                <div class="ms-DocumentCard-details">
                                    <div id="search-results-container" class="teams-search-results">
                                        <div class="ms-panel-content">
                                            <div class="ms-empty-message">No results yet. Use the search form above.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Case Runner Card -->
                            <div class="ms-DocumentCard teams-card">
                                <div class="ms-DocumentCard-title">
                                    <h4 class="ms-font-l">Run Test Case</h4>
                                </div>
                                <div class="ms-DocumentCard-details">
                                    <form id="test-run-form">
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Test Case ID</label>
                                            <input type="text" class="ms-TextField-field" id="test-case-id" placeholder="Enter test case ID (e.g. 3180)">
                                            <div class="ms-TextField-description">Specify the ID of the test case you want to run</div>
                                        </div>

                                        <!-- Projects Dropdown (maps to QA environments) -->
                                        <h5 class="ms-font-m teams-section-title">Projects</h5>
                                        <div class="ms-Dropdown" id="projects-selector">
                                            <select class="ms-Dropdown-select" id="project-select">
                                                <option value="qa02">RGS tests</option>
                                                <option value="qa03">Other</option>
                                            </select>
                                            <div class="ms-TextField-description">Select project type to determine test environment</div>
                                        </div>

                                        <!-- Shell Host Selection -->
                                        <h5 class="ms-font-m teams-section-title">Shell Host</h5>
                                        <div class="ms-Dropdown">
                                            <select class="ms-Dropdown-select" id="shell-host-select">
                                                <option value="jps-qa10-app01" selected>jps-qa10-app01</option>
                                                <option value="jps-qa20-app01">jps-qa20-app01</option>
                                            </select>
                                            <div class="ms-TextField-description">Select the shell host for test execution</div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Active Tests Panel -->
                            <div class="ms-DocumentCard teams-card">
                                <div class="ms-DocumentCard-title">
                                    <h4 class="ms-font-l">Active Tests</h4>
                                    <div class="ms-DocumentCard-activity">
                                        <span class="ms-DocumentCard-activityCount" id="active-tests-count">0</span> active tests
                                    </div>
                                </div>
                                <div class="ms-DocumentCard-details">
                                    <div id="active-tests-panel" class="teams-active-tests">
                                        <div class="ms-panel-content">
                                            <div class="ms-empty-message">No active tests</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="teams-grid-col-4">
                            <!-- Test Status Card -->
                            <div class="ms-DocumentCard teams-card">
                                <div class="ms-DocumentCard-title">
                                    <h4 class="ms-font-l">Test Status</h4>
                                </div>
                                <div class="ms-DocumentCard-details">
                                    <div class="teams-test-stats">
                                        <div class="teams-test-stat">
                                            <span class="teams-test-stat-label">Total Tests</span>
                                            <span class="teams-test-stat-value" id="total-tests">0</span>
                                        </div>
                                        <div class="teams-test-stat">
                                            <span class="teams-test-stat-label">Passed</span>
                                            <span class="teams-test-stat-value teams-test-passed" id="passed-tests">0</span>
                                        </div>
                                        <div class="teams-test-stat">
                                            <span class="teams-test-stat-label">Failed</span>
                                            <span class="teams-test-stat-value teams-test-failed" id="failed-tests">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Runs Card -->
                            <div class="ms-DocumentCard teams-card">
                                <div class="ms-DocumentCard-title">
                                    <h4 class="ms-font-l">Recent Runs</h4>
                                </div>
                                <div class="ms-DocumentCard-details">
                                    <table class="ms-Table teams-table" id="recent-runs-table">
                                        <thead>
                                            <tr>
                                                <th>Test ID</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="recent-runs-body">
                                            <!-- Recent runs will be populated here dynamically -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add React and ReactDOM before Fluent UI -->
    <script src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"></script>
    <!-- Replace Bootstrap JS with Fluent UI JS -->
    <script src="https://unpkg.com/@fluentui/react@8/dist/fluentui-react.js"></script>
    <!-- Load environment configuration first -->
    <script src="environment-config.js"></script>
    <!-- Import the UnifiedApiService -->
    <script src="../shared/services/unified-api-service.js"></script>
    <!-- Regular API Service script must be loaded after UnifiedApiService -->
    <script src="services/api-service.js"></script>
    <!-- Load the test search functionality -->
    <script src="js/test-search.js"></script>
    <script src="config.js"></script>

    <script>
        // Add service worker registration script
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('./service-worker.js').then(function(registration) {
                    console.log('Service worker registered:', registration);
                }).catch(function(registrationError) {
                    console.error('Service worker registration failed:', registrationError);
                });
            });
        } else {
            console.error('Service workers are not supported.');
        }
    </script>
</body>
</html>