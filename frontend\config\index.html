<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Test Runner</title>
    <!-- Replace Bootstrap with Fluent UI -->
    <link href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css" rel="stylesheet">
    <link href="https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-core/11.0.0/css/fabric.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <!-- Custom Test Search Styles -->
    <link rel="stylesheet" href="css/test-search.css" />
    <!-- Load shared services in the correct order -->
    <script type="module" src="../shared/services/base-api-service.js"></script>
    <script src="../shared/services/unified-api-service.js"></script>
    <script src="../shared/services/external-api-service.js"></script>
</head>
<body class="ms-Fabric">
    <!-- Login Modal for API Credentials -->
    <div id="login-modal" class="ms-modal active">
        <div class="ms-modal-content">
            <h2 class="ms-font-xl">Login to Test System</h2>
            <form id="login-form">
                <div class="ms-form-group">
                    <label class="ms-Label">Username</label>
                    <input class="ms-TextField-field" type="text" id="username" placeholder="Enter your username" required>
                </div>
                <div class="ms-form-group">
                    <label class="ms-Label">Password</label>
                    <input class="ms-TextField-field" type="password" id="password" placeholder="Enter your password" required>
                </div>
                <div class="ms-form-actions">
                    <button type="submit" class="ms-Button ms-Button--primary">
                        <span class="ms-Button-label">Login</span>
                    </button>
                    <div class="login-status" id="login-status" style="margin-top: 10px; color: #d13438; display: none;">
                        Invalid credentials. Please try again.
                    </div>
                </div>
            </form>
        </div>
    </div>

    <header class="ms-header">
        <div class="ms-header-title">
            <a class="ms-header-brand" href="#">Test Automation Framework</a>
        </div>
        <div class="ms-header-controls">
            <span class="ms-environment-display" id="environment-display">Environment: Development</span>
            <span class="ms-user-info" id="user-display">Not logged in</span>
            <button class="ms-Button ms-Button--default" id="login-button">
                <span class="ms-Button-label">Login</span>
            </button>
            <button class="ms-Button ms-Button--default" id="logout-button" style="display: none;">
                <span class="ms-Button-label">Logout</span>
            </button>
        </div>
    </header>

    <div class="ms-container">
        <div id="notification-container" style="position: fixed; top: 70px; right: 20px; z-index: 9999; width: 350px;"></div>
        <div class="ms-layout">
            <nav id="sidebarMenu" class="ms-nav">
                <div class="ms-nav-content">
                    <ul class="ms-nav-list">
                        <li class="ms-nav-item">
                            <a class="ms-nav-link" href="../dashboard/index.html">
                                Dashboard
                            </a>
                        </li>
                        <li class="ms-nav-item">
                            <a class="ms-nav-link ms-nav-link-active" href="#">
                                Custom Test Runner
                            </a>
                        </li>
                        <li class="ms-nav-item">
                            <a class="ms-nav-link" href="../reports/index.html">
                                Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="ms-content">
                <div class="ms-content-header">
                    <h1 class="ms-font-xxl">Custom Test Runner</h1>
                </div>

                <!-- Sticky Test Status Bar -->
                <div id="test-status-bar" class="test-status-bar hidden">
                    <div class="status-content">
                        <div class="status-left">
                            <span id="current-test-icon">🟢</span>
                            <span id="current-test-info">No active tests</span>
                            <span id="test-timer" class="status-timer hidden">⏱️ 00:00</span>
                        </div>
                        <div class="status-center">
                            <div id="test-progress-container" class="progress-container hidden">
                                <span id="test-progress-text">0%</span>
                                <div class="progress-bar">
                                    <div id="test-progress-fill" class="progress-fill"></div>
                                </div>
                            </div>
                        </div>
                        <div class="status-right">
                            <button id="view-active-tests-btn" class="ms-Button ms-Button--default status-btn hidden">
                                <span class="ms-Button-label">View Active Tests</span>
                            </button>
                            <button id="view-results-btn" class="ms-Button ms-Button--default status-btn hidden">
                                <span class="ms-Button-label">View Results</span>
                            </button>
                            <button id="test-status-demo-btn" class="ms-Button ms-Button--default status-btn">
                                <span class="ms-Button-label">Demo</span>
                            </button>
                            <button id="dismiss-status-btn" class="ms-Button ms-Button--default status-btn">
                                <span class="ms-Button-label">✕</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="ms-grid">
                    <div class="ms-grid-row">
                        <div class="ms-grid-col ms-sm8">
                            <!-- Test Case Search Card -->
                            <div class="ms-card">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Search Test Cases</h4>
                                </div>
                                <div class="ms-card-body">
                                    <form id="test-search-form">
                                        <div class="ms-form-group">
                                            <label class="ms-Label">Test Case Name</label>
                                            <input type="text" class="ms-TextField-field" id="test-case-name-search" placeholder="Search by name">
                                        </div>

                                        <div class="ms-form-group">
                                            <label class="ms-Label">Test Case Comment</label>
                                            <input type="text" class="ms-TextField-field" id="test-case-comment-search" placeholder="Search in comments (e.g., 'payout')">
                                            <div class="ms-TextField-description">Search for keywords in test case comments</div>
                                        </div>

                                        <div class="ms-form-group">
                                            <label class="ms-Label">Status</label>
                                            <div class="ms-Dropdown">
                                                <select class="ms-Dropdown-select" id="test-case-status-filter">
                                                    <option value="">All</option>
                                                    <option value="Active">Active</option>
                                                    <option value="Inactive">Inactive</option>
                                                    <option value="Maintenance">Maintenance</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="ms-form-group">
                                            <button type="button" class="ms-Button ms-Button--primary" id="search-test-cases-btn" onclick="searchTestCases()">
                                                <span class="ms-Button-label">Search</span>
                                            </button>
                                            <button type="reset" class="ms-Button ms-Button--default">
                                                <span class="ms-Button-label">Clear</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Search Results Card -->
                            <div class="ms-card" id="search-results-card" style="display: none;">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Search Results</h4>
                                    <div class="ms-card-activity">
                                        <span class="ms-card-count" id="search-results-count">0</span> test cases found
                                    </div>
                                </div>
                                <div class="ms-card-body">
                                    <div id="search-results-container" class="search-results">
                                        <div class="ms-panel-content">
                                            <div class="ms-empty-message">No results yet. Use the search form above.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Case Runner Card -->
                            <div class="ms-card">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Run Test Case</h4>
                                </div>
                                <div class="ms-card-body">
                                    <form id="test-run-form">
                                        <div class="ms-form-group">
                                            <label class="ms-Label">Test Case ID</label>
                                            <input type="text" class="ms-TextField-field" id="test-case-id" placeholder="Enter test case ID (e.g. 3180)">
                                            <div class="ms-TextField-description">Specify the ID of the test case you want to run</div>
                                        </div>

                                        <!-- Projects Dropdown (maps to QA environments) -->
                                        <h5 class="ms-font-m ms-section-title">Projects</h5>
                                        <div class="ms-form-group">
                                            <div class="ms-Dropdown" id="projects-selector">
                                                <select class="ms-Dropdown-select" id="project-select">
                                                    <option value="qa02">RGS tests</option>
                                                    <option value="qa03">Other</option>
                                                </select>
                                                <div class="ms-TextField-description">Select project type to determine test environment</div>
                                            </div>
                                        </div>

                                        <!-- Shell Host Selection -->
                                        <h5 class="ms-font-m ms-section-title">Shell Host</h5>
                                        <div class="ms-form-group">
                                            <div class="ms-Dropdown">
                                                <select class="ms-Dropdown-select" id="shell-host-select">
                                                    <option value="jps-qa10-app01" selected>jps-qa10-app01</option>
                                                    <option value="jps-qa20-app01">jps-qa20-app01</option>
                                                </select>
                                                <div class="ms-TextField-description">Select the shell host for test execution</div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Active Tests Panel -->
                            <div class="ms-card">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Active Tests</h4>
                                    <div class="ms-card-activity">
                                        <span class="ms-card-count" id="active-tests-count">0</span> active tests
                                    </div>
                                </div>
                                <div class="ms-card-body">
                                    <div id="active-tests-panel" class="active-tests">
                                        <div class="ms-panel-content">
                                            <div class="ms-empty-message">No active tests</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="ms-grid-col ms-sm4">
                            <!-- Test Status Card -->
                            <div class="ms-card">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Test Status</h4>
                                </div>
                                <div class="ms-card-body">
                                    <div class="test-stats">
                                        <div class="test-stat">
                                            <span class="test-stat-label">Total Tests</span>
                                            <span class="test-stat-value" id="total-tests">0</span>
                                        </div>
                                        <div class="test-stat">
                                            <span class="test-stat-label">Passed</span>
                                            <span class="test-stat-value test-passed" id="passed-tests">0</span>
                                        </div>
                                        <div class="test-stat">
                                            <span class="test-stat-label">Failed</span>
                                            <span class="test-stat-value test-failed" id="failed-tests">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Runs Card -->
                            <div class="ms-card">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Recent Runs</h4>
                                </div>
                                <div class="ms-card-body">
                                    <table class="ms-Table" id="recent-runs-table">
                                        <thead>
                                            <tr>
                                                <th>Test ID</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="recent-runs-body">
                                            <!-- Recent runs will be populated here dynamically -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Floating Action Button for Quick Test Run -->
    <div id="floating-test-runner" class="floating-test-runner">
        <div id="quick-test-panel" class="quick-test-panel hidden">
            <div class="quick-test-header">
                <h4>Quick Test Run</h4>
                <button id="close-quick-test" class="close-btn">✕</button>
            </div>
            <div class="quick-test-body">
                <div class="quick-input-group">
                    <label for="quick-test-id">Test Case ID</label>
                    <input type="text" id="quick-test-id" placeholder="Enter test ID or select from search results" class="quick-input">
                </div>
                <div class="quick-settings-info">
                    <div class="settings-row">
                        <span class="settings-label">Project:</span>
                        <span id="quick-project-display">QA02</span>
                    </div>
                    <div class="settings-row">
                        <span class="settings-label">Shell Host:</span>
                        <span id="quick-shell-display">jps-qa10-app01</span>
                    </div>
                    <div class="settings-note">
                        <small>💡 Change settings in the main form above</small>
                    </div>
                </div>
                <div class="quick-actions">
                    <button id="quick-run-btn" class="quick-run-btn">
                        <span class="btn-icon">▶</span>
                        <span class="btn-text">Run Test</span>
                    </button>
                </div>
            </div>
        </div>
        <button id="fab-main" class="fab-main" title="Quick Test Run (Ctrl+Shift+R)">
            <span class="fab-icon">▶</span>
        </button>
    </div>

    <!-- Load authentication and API services -->
    <script src="config-auth.js"></script>
    <script src="environment-config.js"></script>
    <script src="services/api-service.js"></script>

    <!-- Load UI functionality -->
    <script src="js/status-bar.js"></script>
    <script src="js/floating-action-button.js"></script>
    <script src="js/test-search.js"></script>
    <script src="config.js"></script>

    <script>
        // Add service worker registration script
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('./service-worker.js').then(function(registration) {
                    console.log('Service worker registered:', registration);
                }).catch(function(registrationError) {
                    console.error('Service worker registration failed:', registrationError);
                });
            });
        } else {
            console.error('Service workers are not supported.');
        }
    </script>
</body>
</html>