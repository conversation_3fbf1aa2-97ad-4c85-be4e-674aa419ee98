/**
 * Debug script to test API service functionality
 */

console.log('=== DEBUG API SCRIPT ===');

// Wait for page to load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - checking API service...');
    
    // Check if UnifiedApiService is available
    console.log('window.UnifiedApiService:', typeof window.UnifiedApiService);
    
    // Check if apiService is available
    console.log('window.apiService:', typeof window.apiService);
    
    if (window.apiService) {
        console.log('apiService methods:', Object.keys(window.apiService));
        console.log('apiService.searchTestCases:', typeof window.apiService.searchTestCases);
        console.log('apiService.moduleContext:', window.apiService.moduleContext);
    }
    
    // Try to create a UnifiedApiService instance manually
    if (window.UnifiedApiService) {
        console.log('Creating test UnifiedApiService instance...');
        const testService = new window.UnifiedApiService();
        testService.moduleContext = 'config';
        console.log('Test service methods:', Object.keys(testService));
        console.log('Test service searchTestCases:', typeof testService.searchTestCases);
        
        // Try to call searchTestCases
        if (typeof testService.searchTestCases === 'function') {
            console.log('Testing searchTestCases...');
            testService.searchTestCases({ name: 'test' })
                .then(result => console.log('searchTestCases result:', result))
                .catch(error => console.error('searchTestCases error:', error));
        }
    }
});
