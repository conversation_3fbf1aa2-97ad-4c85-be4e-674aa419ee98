/**
 * Debug script to test API service functionality
 */

console.log('=== DEBUG API SCRIPT ===');

// Wait for page to load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - checking API service...');

    // Check if UnifiedApiService is available
    console.log('window.UnifiedApiService:', typeof window.UnifiedApiService);

    // Check if apiService is available
    console.log('window.apiService:', typeof window.apiService);

    if (window.apiService) {
        console.log('apiService methods:', Object.keys(window.apiService));
        console.log('apiService.searchTestCases:', typeof window.apiService.searchTestCases);
        console.log('apiService.moduleContext:', window.apiService.moduleContext);
    }

    // Check dropdown elements
    console.log('=== CHECKING DROPDOWN ELEMENTS ===');
    const projectSelect = document.getElementById('project-select');
    const shellHostSelect = document.getElementById('shell-host-select');

    console.log('project-select element:', projectSelect);
    console.log('shell-host-select element:', shellHostSelect);

    if (projectSelect) {
        console.log('project-select style.display:', projectSelect.style.display);
        console.log('project-select computed style:', window.getComputedStyle(projectSelect));
        console.log('project-select parent:', projectSelect.parentElement);
    } else {
        console.error('project-select element not found!');
    }

    if (shellHostSelect) {
        console.log('shell-host-select style.display:', shellHostSelect.style.display);
        console.log('shell-host-select computed style:', window.getComputedStyle(shellHostSelect));
        console.log('shell-host-select parent:', shellHostSelect.parentElement);
    } else {
        console.error('shell-host-select element not found!');
    }

    // Check if the form is visible
    const testRunForm = document.getElementById('test-run-form');
    console.log('test-run-form element:', testRunForm);
    if (testRunForm) {
        console.log('test-run-form computed style:', window.getComputedStyle(testRunForm));
    }
});
