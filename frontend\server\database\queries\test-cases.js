/**
 * Test Case Queries
 * Provides functions for querying test cases
 */
const QueryBuilder = require('../utils/query-builder');
const formatter = require('../utils/result-formatter');

/**
 * Get test cases with optional filtering
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Test cases
 */
async function getTestCases(connection, filters = {}) {
  const { ts_id, status, limit = 50, cacheKey } = filters;
  
  // Use cache if available to improve performance
  if (global.testCasesCache && global.testCasesCache[cacheKey || 'default']) {
    console.log('Using cached test cases data');
    return global.testCasesCache[cacheKey || 'default'];
  }

  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query - optimized to retrieve only essential fields initially
  queryBuilder.select('test_case tc', [
    'tc.tc_id',
    'tc.name',
    'tc.status'
  ]);

  // Filter by suite ID if provided
  if (ts_id) {
    queryBuilder.join('test_case_group tcg', 'tc.tc_id = tcg.tc_id');
    queryBuilder.where('tcg.ts_id', '=', ts_id);
  }

  // Filter by status if provided
  if (status) {
    queryBuilder.where('tc.status', '=', status);
  }

  // Add ordering and limit
  queryBuilder.orderBy('tc.tc_id', 'DESC');
  queryBuilder.limit(limit);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    console.time('testCasesQuery');
    const rows = await connection.query(sql, params);
    console.timeEnd('testCasesQuery');
    
    const formattedResults = formatter.formatTestCases(rows);
    
    // Cache the results for future requests
    if (!global.testCasesCache) {
      global.testCasesCache = {};
    }
    global.testCasesCache[cacheKey || 'default'] = formattedResults;
    
    // Set a timeout to clear the cache after 5 minutes
    setTimeout(() => {
      if (global.testCasesCache && global.testCasesCache[cacheKey || 'default']) {
        delete global.testCasesCache[cacheKey || 'default'];
        console.log('Test cases cache cleared');
      }
    }, 5 * 60 * 1000);
    
    return formattedResults;
  } catch (error) {
    console.error('Error executing getTestCases query:', error);

    // Try a simpler fallback query if the main query fails
    try {
      console.log('Attempting fallback query for test cases');
      const fallbackSql = `
        SELECT tc_id, uid, status, name
        FROM test_case
        ORDER BY tc_id DESC
        LIMIT ?
      `;
      console.time('fallbackQuery');
      const fallbackRows = await connection.query(fallbackSql, [limit]);
      console.timeEnd('fallbackQuery');
      
      return formatter.formatTestCases(fallbackRows);
    } catch (fallbackError) {
      console.error('Fallback query also failed:', fallbackError);
      return [];
    }
  }
}

/**
 * Get a test case by ID
 * @param {Object} connection - Database connection
 * @param {number|string} tc_id - Test case ID
 * @returns {Promise<Object>} - Test case
 */
async function getTestCaseById(connection, tc_id) {
  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Build query
  queryBuilder.select('test_case', [
    'tc_id',
    'uid',
    'status',
    'case_driver',
    'tp_id',
    'comments',
    'tickets',
    'name'
  ]);
  queryBuilder.where('tc_id', '=', tc_id);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);

    if (rows.length === 0) {
      throw new Error(`Test case with ID ${tc_id} not found`);
    }

    return formatter.formatTestCases(rows)[0];
  } catch (error) {
    console.error(`Error getting test case by ID ${tc_id}:`, error);
    throw error;
  }
}

/**
 * Search test cases
 * @param {Object} connection - Database connection
 * @param {Object} criteria - Search criteria
 * @returns {Promise<Array>} - Test cases
 */
async function searchTestCases(connection, criteria = {}) {
  const { name, status, min_id, max_id, comments, limit = 20 } = criteria;

  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query
  queryBuilder.select('test_case', [
    'tc_id',
    'uid',
    'status',
    'case_driver',
    'tp_id',
    'comments',
    'tickets',
    'name'
  ]);

  // Add filters
  if (name) {
    queryBuilder.where('name', 'LIKE', `%${name}%`);
  }

  if (status) {
    queryBuilder.where('status', '=', status);
  }

  if (min_id) {
    queryBuilder.where('tc_id', '>=', min_id);
  }

  if (max_id) {
    queryBuilder.where('tc_id', '<=', max_id);
  }

  // Add filter for comments if provided
  if (comments) {
    queryBuilder.where('comments', 'LIKE', `%${comments}%`);
  }

  // Add ordering and limit
  queryBuilder.orderBy('tc_id', 'DESC');
  queryBuilder.limit(limit);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);
    return formatter.formatTestCases(rows);
  } catch (error) {
    console.error('Error searching test cases:', error);
    return [];
  }
}

module.exports = {
  getTestCases,
  getTestCaseById,
  searchTestCases
};
