/**
 * Config API Service - Unified Implementation
 *
 * Direct replacement of the original config API service
 * using the unified service with config context.
 */

// Log the current state before we do anything
console.log('API Service initialization - existing window.apiService:', window.apiService);
console.log('API Service initialization - existing window.UnifiedApiService:', window.UnifiedApiService);

// Create apiService variable to use in this scope
// Important: Do not use ES6 module syntax in this file as it's loaded as classic script
var apiService = window.apiService || {};

if (window.UnifiedApiService) {
  console.log('Setting up apiService using global UnifiedApiService');
  
  // If we don't already have a global apiService with searchTestCases
  if (!window.apiService || typeof window.apiService.searchTestCases !== 'function') {
    console.log('Creating a new apiService instance or enhancing existing one');
    
    // If no global apiService exists yet, create one
    if (!window.apiService) {
      apiService = new window.UnifiedApiService();
    }
    
    // Set the module context and initialize
    apiService.moduleContext = 'config';
    
    // Initialize if the method exists
    if (typeof apiService.initializeConfiguration === 'function') {
      apiService.initializeConfiguration();
    }
    
    // CRITICAL: Ensure searchTestCases is defined
    if (typeof apiService.searchTestCases !== 'function') {
      console.log('Adding searchTestCases method to apiService');
      apiService.searchTestCases = function(criteria) {
        console.log('searchTestCases called with criteria:', criteria);
        // Try using the unified API service method if available
        if (window.UnifiedApiService && window.UnifiedApiService.prototype.searchTestCases) {
          return window.UnifiedApiService.prototype.searchTestCases.call(this, criteria);
        }
        // Use the endpoint directly as fallback
        return this.getRequest('/local/test-cases', criteria || {});
      };
    }
    
    // Make it globally available (preserving existing interface)
    window.apiService = apiService;
  }
  
  console.log('apiService is now configured and available globally');
  console.log('apiService methods:', Object.keys(window.apiService));
  console.log('searchTestCases available:', typeof window.apiService.searchTestCases === 'function');
  
} else {
  console.error('UnifiedApiService not found or not properly initialized! Make sure unified-api-service.js is loaded before this script.');
  
  // Create a placeholder object with error methods to prevent crashes
  apiService = {
    moduleContext: 'config',
    initializeConfiguration: function() { 
      console.error('Unable to initialize configuration: UnifiedApiService not available');
    },
    runTestCase: function() {
      console.error('Cannot run test case: UnifiedApiService not available');
      return Promise.reject('API Service not available');
    },
    getRecentRuns: function() {
      console.error('Cannot get recent runs: UnifiedApiService not available');
      return Promise.reject('API Service not available');
    },
    searchTestCases: function(criteria) {
      console.error('Cannot search test cases: UnifiedApiService not available');
      return Promise.reject('API Service not available');
    }
  };
  
  // Still make the placeholder globally available
  window.apiService = apiService;
}

// IMPORTANT: Ensure searchTestCases is definitely attached to window.apiService
if (typeof window.apiService.searchTestCases !== 'function') {
  console.warn('searchTestCases still not defined on window.apiService, adding it now');
  window.apiService.searchTestCases = function(criteria) {
    console.log('Fallback searchTestCases called with criteria:', criteria);
    return Promise.resolve([]);
  };
}

// Dispatch event to notify that apiService is ready (helps with timing issues)
document.dispatchEvent(new CustomEvent('apiservice-ready', {
  detail: { apiService: window.apiService }
}));

// Global apiService now available via window.apiService
console.log('API Service setup complete - window.apiService is now available');
console.log('Final check - searchTestCases available:', typeof window.apiService.searchTestCases === 'function');