// Custom Test Runner JavaScript

// DOM Elements
const elements = {
    // Test runner controls
    testCaseIdInput: document.getElementById('test-case-id'),
    runTestButton: document.getElementById('run-test-btn'),
    projectSelect: document.getElementById('project-select'),
    shellHostSelect: document.getElementById('shell-host-select'),
    environmentDisplay: document.getElementById('environment-display'),
    
    // Active tests panel
    activeTestsPanel: document.getElementById('active-tests-panel'),
    activeTestsCount: document.getElementById('active-tests-count'),
    
    // Test status elements
    totalTestsCounter: document.getElementById('total-tests'),
    passedTestsCounter: document.getElementById('passed-tests'),
    failedTestsCounter: document.getElementById('failed-tests'),
    
    // Recent runs table
    recentRunsTable: document.getElementById('recent-runs-table'),
    recentRunsBody: document.getElementById('recent-runs-body'),
    environmentConfigs: {
        development: document.getElementById('development-config'),
        staging: document.getElementById('staging-config'),
        production: document.getElementById('production-config')
    },
    n8nBaseUrl: document.getElementById('n8n-base-url'),
    testExecutionWebhook: document.getElementById('test-execution-webhook'),
    nlpWebhook: document.getElementById('nlp-webhook'),
    testResultsWebhook: document.getElementById('test-results-webhook'),
    refreshInterval: document.getElementById('refresh-interval'),
    enableNotifications: document.getElementById('enable-notifications'),
    autoExpandDetails: document.getElementById('auto-expand-details'),
    // Test configuration elements
    smokeTestTimeout: document.getElementById('smoke-test-timeout'),
    smokeTestRetries: document.getElementById('smoke-test-retries'),
    regressionTestTimeout: document.getElementById('regression-test-timeout'),
    regressionTestRetries: document.getElementById('regression-test-retries'),
    heartbeatTestTimeout: document.getElementById('heartbeat-test-timeout'),
    heartbeatTestFrequency: document.getElementById('heartbeat-test-frequency'),
    heartbeatAutoEnable: document.getElementById('heartbeat-auto-enable'),
    // Environment specific elements
    devDbHost: document.getElementById('dev-db-host'),
    devDbName: document.getElementById('dev-db-name'),
    stagingDbHost: document.getElementById('staging-db-host'),
    stagingDbName: document.getElementById('staging-db-name'),
    prodDbHost: document.getElementById('prod-db-host'),
    prodDbName: document.getElementById('prod-db-name')
};

// App state for test runner
let appState = {
    activeTests: new Map(), // Map of active tests by tsn_id
    recentRunsCache: [], // Cache of recent runs data
    pollInterval: null, // Interval for polling recent runs
    credentials: { // User credentials (should be loaded from session/local storage)
        uid: '',
        password: ''
    },
    testRunDefaults: {
        envir: 'qa02',
        shell_host: 'jps-qa10-app01'
    }
};

// Default refresh interval in milliseconds
const REFRESH_INTERVAL = 5000;

// Grace period for keeping completed tests visible (milliseconds)
const COMPLETED_TEST_GRACE_PERIOD = 10000;

// Initialize Custom Test Runner functionality
function initCustomTestRunner() {
    // Set up event listeners for test runner controls
    setupEventListeners();
    
    // Load user credentials from localStorage
    loadCredentials();
    
    // Initialize test counts
    resetTestCounters();
    
    // Start polling for recent runs
    startPolling();
}

// Load user credentials from localStorage or session storage
function loadCredentials() {
    try {
        const storedCredentials = localStorage.getItem('userCredentials');
        if (storedCredentials) {
            appState.credentials = JSON.parse(storedCredentials);
        } else {
            console.log('No stored credentials found');
            // For testing purposes - would normally prompt user for credentials
            appState.credentials = {
                uid: 'testuser',
                password: 'placeholder'
            };
        }
    } catch (error) {
        console.error('Error loading credentials:', error);
    }
}

// Reset test counters to zero
function resetTestCounters() {
    elements.totalTestsCounter.textContent = '0';
    elements.passedTestsCounter.textContent = '0';
    elements.failedTestsCounter.textContent = '0';
}

// Update test counters from recent runs data
function updateTestCounters() {
    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;
    
    // Count tests in the recent runs cache
    appState.recentRunsCache.forEach(test => {
        totalTests++;
        if (test.status === 'passed') passedTests++;
        if (test.status === 'failed') failedTests++;
    });
    
    // Update counter elements
    elements.totalTestsCounter.textContent = totalTests;
    elements.passedTestsCounter.textContent = passedTests;
    elements.failedTestsCounter.textContent = failedTests;
}

// Set up event listeners for test runner controls
function setupEventListeners() {
    // Run test button
    elements.runTestButton.addEventListener('click', handleRunTest);
    
    // Projects dropdown (maps to QA environments)
    elements.projectSelect.addEventListener('change', function() {
        appState.testRunDefaults.envir = this.value;
    });
    
    // Shell host select
    elements.shellHostSelect.addEventListener('change', function() {
        appState.testRunDefaults.shell_host = this.value;
    });
    
    // Set default values
    elements.projectSelect.value = appState.testRunDefaults.envir;
    elements.shellHostSelect.value = appState.testRunDefaults.shell_host;
}

// Handle the Run Test button click
async function handleRunTest() {
    // Get test case ID from input
    const testCaseId = elements.testCaseIdInput.value.trim();
    if (!testCaseId) {
        showError('Please enter a test case ID');
        return;
    }
    
    console.log('Test Case ID (before conversion):', testCaseId);
    console.log('Test Case ID type:', typeof testCaseId);
    
    // Get selected environment and shell host
    const environment = appState.testRunDefaults.envir;
    const shellHost = appState.testRunDefaults.shell_host;
    
    try {
        // Disable button during API call
        elements.runTestButton.disabled = true;
        elements.runTestButton.textContent = 'Running...';
        
        // Call the API to run the test case
        const result = await runTestCase(testCaseId, environment, shellHost);
        
        if (result && result.tsn_id) {
            showSuccess(`Test case ${testCaseId} started. TSN ID: ${result.tsn_id}`);
            
            // Add to active tests
            addActiveTest(result);
            
            // Clear the input field for next test
            elements.testCaseIdInput.value = '';
        } else {
            showError(`Failed to start test case ${testCaseId}`);
        }
    } catch (error) {
        console.error('Error running test case:', error);
        showError(`Error running test case: ${error.message || 'Unknown error'}`);
    } finally {
        // Re-enable button
        elements.runTestButton.disabled = false;
        elements.runTestButton.textContent = 'Run Test';
    }
}

// Run a test case via the API
async function runTestCase(tcId, environment, shellHost) {
    try {
        console.log('runTestCase received tcId:', tcId, 'type:', typeof tcId);
        
        // Ensure tcId is a string or number, not an object
        if (typeof tcId === 'object') {
            console.error('tcId is an object! Converting to string:', tcId);
            tcId = String(tcId);
        }
        
        const apiService = new UnifiedApiService();
        
        // Set up the parameters for the API call (without tc_id which needs to be passed separately)
        const params = {
            uid: appState.credentials.uid,
            password: appState.credentials.password,
            envir: environment,
            shell_host: shellHost
        };
        
        console.log('Running test case with ID:', tcId, 'and params:', params);
        
        // Call the API with tcId as first parameter and params as second parameter
        const response = await apiService.runTestCase(tcId, params);
        return response;
    } catch (error) {
        console.error('API error running test case:', error);
        throw error;
    }
}

// Start polling for recent runs
function startPolling() {
    // Clear any existing interval
    if (appState.pollInterval) {
        clearInterval(appState.pollInterval);
    }
    
    // Poll immediately
    pollRecentRuns();
    
    // Set up polling interval
    appState.pollInterval = setInterval(pollRecentRuns, REFRESH_INTERVAL);
}

// Poll for recent runs data
async function pollRecentRuns() {
    try {
        const apiService = new UnifiedApiService();
        const recentRuns = await apiService.getRecentRuns();
        
        // Update our cache
        appState.recentRunsCache = recentRuns;
        
        // Process the data
        processRecentRunsData(recentRuns);
        
        // Update UI
        renderRecentRuns(recentRuns);
        updateTestCounters();
    } catch (error) {
        console.error('Error polling recent runs:', error);
    }
}

// Process recent runs data to update active tests
function processRecentRunsData(recentRuns) {
    // Check for completed tests
    appState.activeTests.forEach((activeTest, tsnId) => {
        // Find the test in recent runs
        const recentRunData = recentRuns.find(run => run.tsn_id === tsnId);
        
        if (recentRunData) {
            // Update the active test data
            activeTest.data = { ...activeTest.data, ...recentRunData };
            
            // Check if test is completed
            const isCompleted = recentRunData.status === 'passed' || 
                               recentRunData.status === 'failed' ||
                               recentRunData.status === 'error';
            
            if (isCompleted && recentRunData.end_time && !activeTest.completionTimestamp) {
                activeTest.completionTimestamp = Date.now();
                
                // Show notification for completed test
                showTestCompletionNotification(recentRunData);
            }
        }
    });
    
    // Remove completed tests after grace period
    const now = Date.now();
    const testsToRemove = [];
    
    appState.activeTests.forEach((activeTest, tsnId) => {
        if (activeTest.completionTimestamp && 
            (now - activeTest.completionTimestamp) > COMPLETED_TEST_GRACE_PERIOD) {
            testsToRemove.push(tsnId);
        }
    });
    
    testsToRemove.forEach(tsnId => {
        appState.activeTests.delete(tsnId);
    });
    
    // Update active tests panel
    renderActiveTests();
}
// Add a test to the active tests panel
function addActiveTest(testData) {
    // Add to our active tests Map
    appState.activeTests.set(testData.tsn_id, {
        data: testData,
        completionTimestamp: null
    });
    
    // Update UI
    renderActiveTests();
}

// Render all active tests in the panel
function renderActiveTests() {
    // Update the active tests count
    elements.activeTestsCount.textContent = appState.activeTests.size;
    
    // Clear current content
    const activeTestsContent = elements.activeTestsPanel.querySelector('.ms-panel-content');
    activeTestsContent.innerHTML = '';
    
    // If no active tests, show empty message
    if (appState.activeTests.size === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'ms-empty-message';
        emptyMessage.textContent = 'No active tests';
        activeTestsContent.appendChild(emptyMessage);
        return;
    }
    
    // Create cards for each active test
    appState.activeTests.forEach((activeTest) => {
        const testData = activeTest.data;
        const card = createTestCard(testData);
        activeTestsContent.appendChild(card);
    });
}

// Create a test card element
function createTestCard(testData) {
    const card = document.createElement('div');
    card.className = 'ms-card';
    card.dataset.tsnId = testData.tsn_id;
    
    // Determine status class
    let statusClass = 'ms-status-running';
    if (testData.status === 'passed') statusClass = 'ms-status-passed';
    if (testData.status === 'failed') statusClass = 'ms-status-failed';
    if (testData.status === 'error') statusClass = 'ms-status-error';
    
    // Calculate progress percentage
    const totalCases = testData.total_cases || 1;
    const passedCases = testData.passed_cases || 0;
    const failedCases = testData.failed_cases || 0;
    const completedCases = passedCases + failedCases;
    const progressPercent = Math.floor((completedCases / totalCases) * 100);
    
    card.innerHTML = `
        <div class="ms-card-header ${statusClass}">
            <div class="ms-card-title">Test Case: ${testData.tc_id}</div>
            <div class="ms-card-subtitle">TSN ID: ${testData.tsn_id}</div>
            <div class="ms-card-status">${testData.status || 'Running'}</div>
        </div>
        <div class="ms-card-body">
            <div class="ms-progress">
                <div class="ms-progress-bar" style="width: ${progressPercent}%"></div>
            </div>
            <div class="ms-progress-text">
                ${passedCases} / ${totalCases} Passed, ${failedCases} Failed
            </div>
        </div>
        <div class="ms-card-footer">
            <button class="ms-button ms-button-small ms-button-danger stop-test" data-tsn-id="${testData.tsn_id}">Stop Test</button>
        </div>
    `;
    
    // Add event listener for Stop Test button
    const stopButton = card.querySelector('.stop-test');
    stopButton.addEventListener('click', function() {
        stopTest(this.dataset.tsnId);
    });
    
    return card;
}

// Render recent runs in the table
function renderRecentRuns(recentRuns) {
    // Clear existing rows
    elements.recentRunsBody.innerHTML = '';
    
    // Add each run as a row
    recentRuns.forEach(run => {
        const row = document.createElement('tr');
        
        // Format timestamp
        const timestamp = run.start_time ? new Date(run.start_time).toLocaleString() : 'N/A';
        
        // Determine status class
        let statusClass = '';
        if (run.status === 'passed') statusClass = 'ms-text-success';
        if (run.status === 'failed') statusClass = 'ms-text-danger';
        if (run.status === 'error') statusClass = 'ms-text-danger';
        
        row.innerHTML = `
            <td>${run.tc_id}</td>
            <td>${run.tsn_id}</td>
            <td class="${statusClass}">${run.status || 'Running'}</td>
            <td>${timestamp}</td>
            <td>
                <button class="ms-button ms-button-small view-details" data-tsn-id="${run.tsn_id}">View Details</button>
            </td>
        `;
        
        // Add event listener for View Details button
        const viewDetailsButton = row.querySelector('.view-details');
        viewDetailsButton.addEventListener('click', function() {
            viewTestDetails(this.dataset.tsnId);
        });
        
        elements.recentRunsBody.appendChild(row);
    });
}

// Stop a running test
async function stopTest(tsnId) {
    try {
        const apiService = new UnifiedApiService();
        await apiService.stopTest(tsnId);
        showSuccess(`Stopping test ${tsnId}...`);
    } catch (error) {
        console.error('Error stopping test:', error);
        showError(`Failed to stop test: ${error.message || 'Unknown error'}`);
    }
}

// View test details
function viewTestDetails(tsnId) {
    // Find the test in recent runs
    const testData = appState.recentRunsCache.find(run => run.tsn_id === tsnId);
    
    if (testData && testData.details_url) {
        window.open(testData.details_url, '_blank');
    } else {
        showError('Test details not available');
    }
}

// Show test completion notification
function showTestCompletionNotification(testData) {
    const status = testData.status || 'completed';
    const passedCount = testData.passed_cases || 0;
    const failedCount = testData.failed_cases || 0;
    
    const message = `Test ${testData.tc_id} ${status}. ` + 
                   `Cases Passed: ${passedCount}, Cases Failed: ${failedCount}`;
    
    if (status === 'passed') {
        showSuccess(message);
    } else {
        showError(message);
    }
}

// Show success message
function showSuccess(message) {
    const toast = document.createElement('div');
    toast.className = 'ms-toast ms-toast-success';
    toast.innerHTML = `
        <div class="ms-toast-icon">✓</div>
        <div class="ms-toast-message">${message}</div>
    `;
    
    document.body.appendChild(toast);
    
    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // Hide and remove the toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Show error message
function showError(message) {
    const toast = document.createElement('div');
    toast.className = 'ms-toast ms-toast-error';
    toast.innerHTML = `
        <div class="ms-toast-icon">!</div>
        <div class="ms-toast-message">${message}</div>
    `;
    
    document.body.appendChild(toast);
    
    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // Hide and remove the toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initCustomTestRunner);